from typing import List

from payment.clients.base_external_client import BaseExternalClient
from payment.clients.client_dtos.tenat_config_dto import TenantConfigDto
from django.conf import settings


class CatalogClient(BaseExternalClient):
    page_map = {
        'get_tenant_configs': dict(type=BaseExternalClient.CallTypes.GET,
                                   url_regex="/cataloging-service/api/v1/tenant-configs"),
    }

    def get_domain(self):
        return settings.CATALOG_SERVICE_ENDPOINT_URL

    def get_tenant_configs(self) -> List[TenantConfigDto]:
        page_name = "get_tenant_configs"
        response = self.make_call(page_name=page_name)
        if not response.is_success():
            raise Exception(
                "Catalog API Error. Status Code: {0}, Errors: {1}".format(response.response_code, response.errors))
        return [TenantConfigDto(config.get('config_name'), config.get('config_value'), config.get('value_type')) for
                config in response.json_response]

    def get_specific_tenant_config(self, requested_tenant_config):
        tenant_configs = self.get_tenant_configs()
        tenant_config_dict = {config.config_name: config for config in tenant_configs}
        return tenant_config_dict.get(requested_tenant_config)
