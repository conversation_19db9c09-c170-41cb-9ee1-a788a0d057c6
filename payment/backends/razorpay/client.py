import json
import logging
import requests
import time
import random
from functools import wraps

from backends import PaymentGateways

logger = logging.getLogger(__name__)


def razorpay_retry(max_retries=3, base_delay=1.0, max_delay=10.0, backoff_factor=2.0):
    """
    Decorator to retry Razorpay API calls on specific transient errors.

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Exponential backoff multiplier
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e

                    # Check if this is a retryable error
                    if not _is_retryable_error(e):
                        logger.warning(f"Non-retryable error in {func.__name__}: {str(e)}")
                        raise e

                    if attempt == max_retries:
                        logger.error(f"Max retries ({max_retries}) exceeded for {func.__name__}: {str(e)}")
                        raise e

                    # Calculate delay with exponential backoff and jitter
                    delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                    jitter = random.uniform(0.1, 0.3) * delay
                    total_delay = delay + jitter

                    logger.warning(f"Retryable error in {func.__name__} (attempt {attempt + 1}/{max_retries + 1}): {str(e)}. "
                                 f"Retrying in {total_delay:.2f} seconds...")

                    time.sleep(total_delay)

            # This should never be reached, but just in case
            raise last_exception

        return wrapper
    return decorator


def _is_retryable_error(error):
    """
    Determine if an error is retryable based on the error message or type.

    Args:
        error: The exception that occurred

    Returns:
        bool: True if the error should be retried, False otherwise
    """
    error_str = str(error).lower()

    # Check for specific Razorpay transient errors
    retryable_patterns = [
        "another payment operation is in progress",
        "bad_request_error",
        "timeout",
        "connection error",
        "temporary failure",
        "service unavailable",
        "rate limit",
        "too many requests"
    ]

    for pattern in retryable_patterns:
        if pattern in error_str:
            return True

    # Check for HTTP status codes that are retryable
    if hasattr(error, 'response') and error.response:
        status_code = error.response.status_code
        # Retry on 5xx server errors and specific 4xx errors
        if status_code >= 500 or status_code in [408, 429]:
            return True

    return False


# Temporary class to handle payment patch as Razorpay client does not have a method for patching
class RazorPayClient:
    BASE_URL = "https://api.razorpay.com/v1/"
    PATCH_PAYMENT = "payments/{}/"

    def __init__(self, auth_token):
        self.access_key = auth_token["access_key"]
        self.secret_key = auth_token["secret_key"]

    @razorpay_retry(max_retries=3, base_delay=2.0, max_delay=15.0)
    def patch_payment(self, payment_id, data):
        logger.info('Calling patch payment for payment: %s', payment_id)
        page_name = self.PATCH_PAYMENT.format(payment_id)
        url = self.BASE_URL + page_name
        logger.debug('Calling Razorpay Api api Url: %s and Payload: %s', url, data)

        response = requests.patch(url=url, data=json.dumps(data), headers={'Content-Type': 'application/json'},
                                  auth=(self.access_key, self.secret_key))

        if not response or not response.ok:
            error_msg = f'Error while calling razor pay for url: {url}, payload: {data}, response: {response.text}'
            logger.error(error_msg)

            # Parse the response to check for specific errors
            try:
                response_json = response.json() if response.text else {}
                if 'error' in response_json:
                    error_details = response_json['error']
                    if (error_details.get('description') == "Request failed because another payment operation is in progress" or
                        error_details.get('code') == "BAD_REQUEST_ERROR"):
                        # Raise an exception that will be caught by the retry decorator
                        raise Exception(f"Razorpay API Error: {error_details.get('description', 'Unknown error')}")
            except (json.JSONDecodeError, AttributeError):
                pass

            # For other errors, raise a generic exception
            raise Exception(f"Razorpay API call failed with status {response.status_code}: {response.text}")

        logger.debug('Razor pay response for patch payment: %s', response.json())
        return response.json()
