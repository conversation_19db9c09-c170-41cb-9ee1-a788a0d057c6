#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    RazorpayBackend
"""
import json
import logging
from datetime import datetime
from razorpay.errors import BadRequestError
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from backends.razorpay.client import RazorPayClient
from backends.razorpay.request.fetch_payment import FetchPaymentsRequest
from backends.razorpay.request.patch_payment import PatchPaymentRequest
from common.account import AccountService
from backends.dto.card import CardDTO
from backends.dto.order import OrderResponseDTO
from backends.dto.payment import PaymentResponseDTO
from backends.dto.refund import RefundResponseDTO
from backends.exceptions import RazorpayLinkOrderCreationFailedException, UserAlreadyExistsError, \
    NoCardsFoundForUserError, RazorpayServer2SeverCallbackDataParseException, PaymentInitFailedError, \
    PaymentFinalizeFailedError, OrderCreationFailed, OrderMetaNotFoundException, PaymentMetaNotFoundException, \
    BadRequestException
from backends.interface import PaymentGatewayBackend
from backends.razorpay.constants import RazorPayOrderStatus, RazorPayRefundStatus, RazorPayPaymentStatus, \
    DEFAULT_GUEST_NAME, CARD, \
    AMOUNT_FOR_WHICH_TAX_NOT_APPLICABLE_RAZORPAY, WALLET, EMI, CARDLESS_EMI, BUSINESS, UPI, RazorPayCardTypes, \
    RazorPayPayerAccountTypes
from backends.razorpay.models import RazorpayOrderMeta, RazorpayPaymentMeta, RazorpayRefundMeta, \
    GatewaysCommissionConfiguration
from backends.razorpay.request.refund import FetchInvoiceOrderRequest, FetchRefundsRequest
from common.exceptions import RefundRequestFailed, RefundAlreadyProcessed
from paynow.constants import OrderStatus, RefundStatus, PaymentStatus
from paynow.dto import CustomerRequestDTO, GetCustomerCardsDTO, GetCardDTO, \
    InvoiceRequestDTO
from paynow.models import Payment, Refund, Order, PayoutLink
from paynow.utils import convert_rupee_to_paise, convert_paise_to_rupee
from .razorpay_request_executer import RazorpayRequestExecuter
from .request import CreateOrderRequest, CreateCustomerRequest, CaptureRequest, CustomerCardsRequest, CardRequest, \
    CreateLinkOrderRequest, RefundRequest, InvoiceRequest, VerifyRequest, FetchPaymentRequest
from backends import PaymentGateways
from backends.razorpay.request.payout_link import (
    CreatePayoutLinkRequest,
    PayoutLinkCancellationRequest,
    PayoutLinkFetchRequest,
)
from backends.dto.payout_link import PayoutLinkResponseDTO
from .utils import SlackAlert

logger = logging.getLogger(__name__)


class RazorpayBackend(PaymentGatewayBackend):
    """
        RazorpayBackend
    """
    name = PaymentGateways.RAZORPAY

    def __init__(self, credentials):
        self._executer = RazorpayRequestExecuter(auth_token=credentials)
        self._client = RazorPayClient(auth_token=credentials)

    def create_order(self, order, **kwargs):
        """
        :param order_request_dto:
        :return:
        """
        pg_meta = kwargs.get('pg_meta')
        payment_capture = 1
        if pg_meta and pg_meta.get('auto_capture'):
            auto_capture = pg_meta.get('auto_capture')
            if auto_capture is False:
                payment_capture = 0
        try:
            payment_link_reference_id = ''
            if pg_meta and pg_meta.get('generate_link'):
                response = self._create_order_with_link(order, pg_meta)
                pg_order_id = response.get('order_id')
                payment_link_reference_id = response.get('id')
            else:
                response = self._create_order(
                    order, pg_meta, payment_capture=payment_capture)
                pg_order_id = response.get('id')
            # update razor pay meta info
            rz_meta = RazorpayOrderMeta.objects.create(
                order=order,
                pg_order_id=pg_order_id,
                is_auto_capture=payment_capture,
                created_at=datetime.fromtimestamp(
                    response.get('created_at')),
                pg_signature=response.get('pg_signature'),
                pg_customer_id=response.get('pg_customer_id'),
                status=response.get('status'),
                payment_link=response.get('short_url'),
                payment_link_reference_id=payment_link_reference_id)
        except Exception as e:
            raise OrderCreationFailed(
                "Order creation for razorpay failed for order_id:{} with errors: {}" .format(
                    order.order_id, str(e)))

        data = dict(amount=order.amount,
                    pg_order_id=rz_meta.pg_order_id,
                    created_at=rz_meta.created_at,
                    notes=json.loads(order.notes) if order.notes else [],
                    status=OrderStatus.ORDER_SUCCESS if rz_meta.status
                    in RazorPayOrderStatus.success else OrderStatus.ORDER_FAILED,
                    pg_meta=dict(auto_capture=True if payment_capture == 1 else False,
                                 pg_signature=rz_meta.pg_signature,
                                 pg_customer_id=rz_meta.pg_customer_id,
                                 payment_link=response.get('short_url')),
                    platform_fee=order.platform_fee,
                    amount_after_platform_fee=order.amount_after_platform_fee)

        return OrderResponseDTO(data=data)

    def _create_order(self, order, pg_meta, payment_capture):
        """
        Creates order in razorpay
        Args:
            order:
            pg_meta:

        Returns:

        """
        notes = []
        if order.notes:
            notes = json.loads(order.notes)

        order_request = CreateOrderRequest(
            amount=order.amount_after_platform_fee,
            currency=order.currency,
            receipt=order.receipt,
            payment_capture=payment_capture,
            notes=json.loads(order.notes) if order.notes else []
        )
        return self._executer.execute(order_request, action='create')

    def _create_order_with_link(self, order, pg_meta):
        """
        Create order with link. Razorpay has a different api which creates order along with link. To create a link
        invoices api is called from razorpay which is misleading. It does not create an invoice unless type = invoice
        is specified. For this reason, generating link is being treated as another type of order creation. Request
        and response for this method remains the same as order with addition to link parameter.
        Args:
            order:
            **kwargs:

        Returns:

        """
        payment_link = ''
        customer_name = ''
        customer = pg_meta.get('customer')
        if customer:
            customer_name = customer.get('name')
        if not (customer_name or pg_meta.get('pg_customer_id')):
            raise RazorpayLinkOrderCreationFailedException(
                'Cannot create invoice on razorpay, at least one of '
                'customer name of customer id is required')

        def sanitized_customer_name(customer_name):
            if not customer_name:
                return DEFAULT_GUEST_NAME
            elif len(customer_name) <= 3:
                return DEFAULT_GUEST_NAME
            elif len(customer_name) >= 50:
                return customer_name[:49]
            else:
                return customer_name

        customer = {
            'email': pg_meta.get('email', '<EMAIL>'),
            'contact': pg_meta.get('phone', '9234567891'),
            'name': sanitized_customer_name(customer_name)
        }

        generate_invoice_request = CreateLinkOrderRequest(
            customer=customer,
            customer_id=pg_meta.get('pg_customer_id'),
            amount=order.amount_after_platform_fee,
            currency=order.currency,
            req_type='link',
            description=order.notes,
            notes=json.loads(order.notes) if order.notes else [],
            expire_by=pg_meta.get('expire_by')
        )

        response = self._executer.execute(generate_invoice_request, 'create')
        if response['status'] == 'issued':
            response['status'] = 'created'
        return response

    def create_customer(self, customer_request_dto):
        """
        :param customer_request_dto:
        :return:
        """
        assert isinstance(customer_request_dto, CustomerRequestDTO)
        data = customer_request_dto.data
        customer_request = CreateCustomerRequest(
            name=data['name'],
            email=data['email'],
            phone=data['phone'],
            notes=data['notes'],
            fail_existing="0"
        )
        response = self._executer.execute(
                customer_request, action='create')
        return response

    def make_refund(self, refund, **kwargs):
        """

        :param refund:
        :param kwargs:
        :return:
        """
        notes = {}
        if kwargs.get('notes') and isinstance(kwargs.get('notes'), list):
            notes = {"meta": kwargs.get('notes')[0]}
        refund_request = RefundRequest(
            payment_id=refund.payment.pg_payment_id,
            amount=refund.amount,
            notes=notes
        )
        try:
            response = self._executer.execute(refund_request, action='create')
        except BadRequestError as e:
            if e == "The refund amount provided is greater than the un-refunded amount":
                raise RefundAlreadyProcessed(refund_request.payment_id,
                                             refund.amount,
                                             str(e))
            else:
                raise RefundRequestFailed(refund_request.payment_id,
                                          refund.amount,
                                          str(e))
        except Exception as e:
            raise RefundRequestFailed(refund_request.payment_id,
                                      refund.amount,
                                      str(e))

        # update razor pay refund info

        if isinstance(refund, Refund):
            rz_meta = RazorpayRefundMeta.objects.create(
                refund=refund,
                pg_refund_id=response.get('id'),
                amount=refund_request.amount,
                notes=kwargs.get('notes'),
                status=RazorPayRefundStatus.REFUND_SUCCESS)
            return RefundResponseDTO(
                data=dict(
                    refund_id=response.get('id'),
                    payment_id=refund.payment.pg_payment_id,
                    amount=refund.amount,
                    notes=rz_meta.notes,
                    status=RefundStatus.REFUND_COMPLETED if rz_meta.status in RazorPayRefundStatus.success else
                    RefundStatus.REFUND_FAILED))
        else:
            return RefundResponseDTO(
                data=dict(
                    refund_id=response.get('id'),
                    payment_id=refund.payment.pg_payment_id,
                    amount=refund.amount,
                    notes=refund.notes,
                    status=RefundStatus.REFUND_COMPLETED))

    def get_invoice(self, invoice_request_dto):
        """
        :param invoice_request_dto:
        :return:
        """
        assert isinstance(invoice_request_dto, InvoiceRequestDTO)
        data = invoice_request_dto.data
        invoice_request = InvoiceRequest(
            invoice_id=data.get('invoice_id')
        )
        invoice_details = self._executer.execute(
            invoice_request, action='fetch')

        invoice_details['status'] = PaymentStatus.PAYMENT_CONFIRMED if invoice_details.get(
            'status') == 'paid' else PaymentStatus.PAYMENT_FAILED
        return invoice_details

    def get_customer_cards(self, get_customer_cards_dto):
        """
        :param get_customer_cards_dto:
        :return:
        """
        assert isinstance(get_customer_cards_dto, GetCustomerCardsDTO)
        data = get_customer_cards_dto.data
        customer_card_request = CustomerCardsRequest(
            pg_customer_id=data['pg_customer_id'],
        )
        try:
            response = self._executer.execute(
                customer_card_request, action='all')
        except BadRequestError:
            raise NoCardsFoundForUserError('No cards found for the user')
        items = response['items']
        card_data = []
        for item in items:
            card_details = item['card']
            card_data.append(
                dict(
                    name=card_details['name'],
                    last4=card_details['last4'],
                    network=card_details['network'],
                    expiry_month=card_details['expiry_month'],
                    expiry_year=card_details['expiry_year'],
                    issuer=card_details.get('issuer') if card_details.get('issuer') else '',
                    id=item['id'],
                    timestamp=item['created_at'],
                    used_at=item.get('used_at') if card_details.get('issuer') else ''))

        return CardDTO(data=card_data, many=True)

    def get_card(self, get_card_dto):
        """
        :param get_card_dto:
        :return:
        """
        assert isinstance(get_card_dto, GetCardDTO)

        data = get_card_dto.data
        customer_request = CardRequest(
            pg_customer_id=data['customer_id'],
            pg_card_id=data['id']
        )
        response = self._executer.execute(customer_request, action='all')
        return response

    def init_payment(self, payment, **kwargs):
        """
        :param payment:
        :return:
        """
        try:
            order_meta = RazorpayOrderMeta.objects.get(order=payment.order)

            rz_payment_meta = RazorpayPaymentMeta.objects.create(
                payment=payment,
                order_meta=order_meta,
                is_verified=False,
                is_captured=False,
                pg_signature='',
                status=RazorPayPaymentStatus.PAYMENT_CREATED
            )

        except Exception as e:
            msg = 'exception while initiating the payment ' \
                  'for {order_id} with exception ' \
                  'as {e}'.format(order_id=payment.order_id, e=e)
            logger.exception(msg)
            raise PaymentInitFailedError(msg)

        return PaymentResponseDTO(
            data=dict(
                pg_payment_id=rz_payment_meta.pg_payment_id,
                pg_meta=dict(
                    redirect_url=''),
                status=rz_payment_meta.status))

    def finalize_payment(self, payment, paid_amount, **kwargs):
        """
        Do nothing for the capture as the backed does auto capture.
        Verify the signature for captured amount.
        Args:
            payment: payment dto
            paid_amount: paid_amount

        Returns:

        """

        assert isinstance(payment, Payment)

        pg_meta = kwargs.get('pg_meta')

        if not pg_meta:
            raise KeyError("PG meta cannot be null")

        try:
            meta_payment = RazorpayPaymentMeta.objects.get(payment=payment)
        except RazorpayPaymentMeta.DoesNotExist:
            raise PaymentFinalizeFailedError('Payment Meta not found')

        if meta_payment.status == RazorPayPaymentStatus.PAYMENT_CAPTURED and meta_payment.pg_payment_id:
            # payment is already verified
            return PaymentResponseDTO(
                data=dict(
                    pg_payment_id=meta_payment.pg_payment_id,
                    pg_meta=dict(),
                    status=meta_payment.status))

        meta_payment.pg_payment_id = pg_meta.get('pg_payment_id')
        meta_payment.save()

        # check if it is auto capture. if not then capture it here
        try:
            if meta_payment.order_meta.is_auto_capture != '1':
                capture_request = CaptureRequest(
                    payment_id=meta_payment.pg_payment_id,
                    amount=paid_amount
                )
                response = self._executer.execute(capture_request, 'capture')
                logger.info(
                    "Response for capture request for payment {}: {}".format(meta_payment.pg_payment_id, response))

                meta_payment.status = RazorPayPaymentStatus.PAYMENT_CAPTURED
                meta_payment.gateway_charges = convert_paise_to_rupee(response.get('fee')) if response.get(
                    'fee') else None
                meta_payment.gateway_charges_tax = convert_paise_to_rupee(response.get('tax')) if response.get(
                    'tax') else None
                meta_payment.save()
            else:
                fetch_payment = FetchPaymentRequest(
                    payment_id=meta_payment.pg_payment_id,
                    expand_on_field=CARD
                )
                response = self._executer.execute(
                    fetch_payment, action='fetch')
                logger.info(
                    "Response for fetch request for payment {}: {}".format(meta_payment.pg_payment_id, response))

                meta_payment.gateway_charges = convert_paise_to_rupee(response.get('fee')) if response.get(
                    'fee') else None
                meta_payment.gateway_charges_tax = convert_paise_to_rupee(response.get('tax')) if response.get(
                    'tax') else None
                calculated_gateway_charges, calculated_gateway_charges_tax, commission_percentage = \
                    self.get_gateway_charges_and_tax(response)

                if calculated_gateway_charges == 0:
                    SlackAlert.send_alert(
                        "Alert!! Not able to find commission configuration for payment: {0}".format(
                            meta_payment.pg_payment_id),
                        tenant_id='Treebo')

                diff_bw_trb_cal_fee_razorpay_cal_fee = abs(
                    calculated_gateway_charges - (
                            (meta_payment.gateway_charges if meta_payment.gateway_charges else 0) - (
                        meta_payment.gateway_charges_tax if meta_payment.gateway_charges_tax else 0)))

                if diff_bw_trb_cal_fee_razorpay_cal_fee > 1 and meta_payment.gateway_charges:
                    SlackAlert.send_alert(
                        "Alert!! Calculation difference between razorpay fee "
                        "for payment: {0} \n Calculated Charge: {1} \n Razorpay Fee : {2} \n "
                        "Payment Method : {3} \n Payment Amount : {4} \n Commission Percentage : {5}".format(
                            meta_payment.pg_payment_id, calculated_gateway_charges,
                            (meta_payment.gateway_charges - meta_payment.gateway_charges_tax if meta_payment.gateway_charges_tax else 0.0),
                            response.get('method', None),
                            convert_paise_to_rupee(response.get('amount')) if response.get('amount') else None,
                            commission_percentage
                        ),
                        tenant_id='Treebo')
                meta_payment.calculated_gateway_charges = calculated_gateway_charges
                meta_payment.calculated_gateway_charges_tax = calculated_gateway_charges_tax

                if pg_meta.get('validate_using_amount'):
                    meta_payment.status = response.get('status')
                    meta_payment.save()

                    # patch notes to payment1
                    order = Order.objects.get(pg_order_id=response.get('order_id'))
                    patch_payment_request = PatchPaymentRequest(notes=dict(booking_id=order.receipt))
                    try:
                        self._client.patch_payment(meta_payment.pg_payment_id, patch_payment_request.json_data())
                        logger.info(f"Successfully patched payment {meta_payment.pg_payment_id} with booking_id {order.receipt}")
                    except Exception as patch_error:
                        logger.error(f"Failed to patch payment {meta_payment.pg_payment_id} after retries: {str(patch_error)}")
                        # Re-raise the exception to maintain existing error handling behavior
                        raise patch_error

                    # verify payment here
                    payment_success = True
                    failed_reason = ''
                    if response.get('amount') != convert_rupee_to_paise(
                            paid_amount):
                        payment_success = False
                        failed_reason = "Amount does not match"
                    if response.get(
                            'order_id') != meta_payment.payment.order.pg_order_id:
                        payment_success = False
                        failed_reason = "order_id does not match"
                    if response.get(
                            'status') not in RazorPayPaymentStatus.success:
                        payment_success = False
                        failed_reason = 'payment status is {} which is not one of {}'.format(
                            meta_payment.status, RazorPayPaymentStatus.success)
                    if not payment_success:
                        raise RuntimeError(failed_reason)

                else:
                    meta_payment.pg_signature = pg_meta.get('pg_signature')
                    meta_payment.save()
                    verify_request = VerifyRequest(
                        razorpay_order_id=payment.order.pg_order_id,
                        razorpay_payment_id=meta_payment.pg_payment_id,
                        razorpay_signature=meta_payment.pg_signature
                    )
                    response = self._executer.execute(
                        verify_request, action='verify_payment_signature')

                    logger.info(
                        "Response for verification request for payment {}: {}".format(meta_payment.pg_payment_id,
                                                                                      response))

                    meta_payment.status = RazorPayPaymentStatus.PAYMENT_CAPTURED
                    meta_payment.save()
        except Exception as e:
            raise PaymentFinalizeFailedError(str(e))

        return PaymentResponseDTO(
            data=dict(
                pg_payment_id=meta_payment.pg_payment_id,
                pg_meta=dict(),
                status=meta_payment.status))

    def get_gateway_charges_and_tax(self, payment_info):
        is_gst_applicable = True
        amount = convert_paise_to_rupee(payment_info.get('amount'))
        method = payment_info.get('method').lower()
        card_type = ''
        sub_type = ''
        payment_mode = ''
        distinct_payment_modes_defined = list(
            GatewaysCommissionConfiguration.objects.filter(payment_method=method).order_by().exclude(
                payment_mode__isnull=True).exclude(payment_mode__exact='').values_list('payment_mode',
                                                                                       flat=True).distinct())
        if method == CARD:
            is_gst_applicable = False if amount <= AMOUNT_FOR_WHICH_TAX_NOT_APPLICABLE_RAZORPAY else True
            card_type = payment_info.get('card')['type']
            sub_type = payment_info.get('card')['sub_type'] if payment_info.get('card')['sub_type'] == BUSINESS else ''
            payment_mode = 'international' if payment_info.get('card')['international'] is True else \
                payment_info.get('card')['network']

        elif method == WALLET:
            payment_mode = payment_info.get('wallet')

        elif method == EMI:
            payment_mode = payment_info.get('card')['network'] if payment_info.get('card') else ''

        elif method == CARDLESS_EMI:
            payment_mode = payment_info.get('wallet') if payment_info.get('wallet') else ''

        elif method == UPI:
            payer_account_type = payment_info.get('upi', {}).get('payer_account_type') or ''
            card_type = RazorPayCardTypes.CREDIT_CARD \
                if payer_account_type == RazorPayPayerAccountTypes.CREDIT_CARD else ''

        else:
            payment_mode = payment_info.get('bank')

        payment_mode = payment_mode.lower() if payment_mode and (
                payment_mode.lower() in distinct_payment_modes_defined) else ''

        return self.calculate_gateway_charges_and_tax(amount, is_gst_applicable, method, card_type, sub_type,
                                                      payment_mode)

    def s2s_callback(self, callback_data):

        try:
            from paynow.services import OrderService

            order_id = callback_data['payment']['entity']['order_id']
            pg_meta = dict(
                validate_using_amount=True,
                pg_payment_id=callback_data['payment']['entity']['id'])

            seller_order_id = OrderService.get_order_id_by_pg_order_id(order_id)

            request_for_confirming_order = dict(
                order_id=order_id,
                pg_meta=pg_meta,
                payment_id=callback_data['payment']['entity']['id'],
                amount=float(
                    callback_data['payment']['entity']['amount']) / 100,
                bank=callback_data['payment']['entity'].get('bank'),
                sellerOrderId=seller_order_id)

            pg_meta_data = dict(
                sellerOrderId=seller_order_id,
                payment_id=callback_data['payment']['entity']['id'],
                created_at=callback_data['payment']['entity']['created_at'],
                invoice_id=callback_data['payment']['entity'].get('invoice_id'),
                event_type=callback_data['event_type'],
                amount=float(
                    callback_data['payment']['entity']['amount']) / 100)

            return request_for_confirming_order, pg_meta_data

        except Exception as e:
            logger.exception('Exception while processing the notification data for razorpay')
            raise RazorpayServer2SeverCallbackDataParseException(
                'Exception while processing the ' 'notification data for razorpay')

    def get_order_from_invoice(self, invoice_id):
        response = self._executer.execute(FetchInvoiceOrderRequest(invoice_id=invoice_id), action= 'fetch')
        return response["order_id"], response["payment_id"]

    def fetch_captured_and_refunded_payments(self, start_time, end_time):

        response, payments, skip = {}, [], 0
        logger.info('Calling Razorpay API to fetch payments start_time: %s and end_time: %s', start_time, end_time)

        while response.get('count') == 100 or not skip:
            fetch_payments = FetchPaymentsRequest(start_time=start_time, end_time=end_time, skip=skip)
            response = self._executer.execute(fetch_payments, action='all')
            payment_items = response.get('items', None)
            if payment_items:
                payments = payments + payment_items
            skip += 100

        captured_and_refunded_payments = self.extract_captured_and_refunded_payments(payments)
        return captured_and_refunded_payments

    @staticmethod
    def extract_captured_and_refunded_payments(payments):
        captured_and_refunded_payments = []

        for payment in payments:
            if payment.get('status') in [RazorPayPaymentStatus.PAYMENT_CAPTURED,
                                         RazorPayPaymentStatus.PAYMENT_REFUNDED]:
                captured_and_refunded_payments.append(payment)

        return captured_and_refunded_payments

    def invalidate_payment_link(self, order_reference_id):
        """
            :param order_reference_id:
            :return:
        """
        try:
            order_meta_details = RazorpayOrderMeta.objects.get(order_id=order_reference_id)
            if not order_meta_details:
                raise OrderMetaNotFoundException("Order meta not found for order reference id {}".
                                                 format(order_reference_id))
            payment_meta_details = RazorpayPaymentMeta.objects.filter(order_meta_id=order_meta_details.id).first()
            if not payment_meta_details:
                raise PaymentMetaNotFoundException("Payment meta not found for order meta id {}".
                                                   format(order_meta_details.id))
            invoice_request = InvoiceRequest(
                invoice_id=order_meta_details.payment_link_reference_id
            )
            self._executer.execute(
                invoice_request, action='cancel')

            order_meta_details.status = RazorPayOrderStatus.ORDER_CANCELLED
            order_meta_details.save()
            payment_meta_details.status = RazorPayPaymentStatus.PAYMENT_CANCELLED
            payment_meta_details.save()

        except Exception as e:
            raise BadRequestException(str(e))

    def fetch_completed_refunds(self, start_time, end_time):
        response, refunds, skip = {}, [], 0
        logger.info('Calling Razorpay API to fetch refunds start_time: %s and end_time: %s', start_time, end_time)

        while response.get('count') == 100 or not skip:
            fetch_refunds = FetchRefundsRequest(start_time=start_time, end_time=end_time, skip=skip)
            response = self._executer.execute(fetch_refunds, action='all')
            refunds_response = response.get('items', None)
            if refunds_response:
                refunds = refunds + refunds_response
            skip += 100
        return [refund for refund in refunds if refund.get('status') == RazorPayRefundStatus.REFUND_PROCESSED]

    def fetch_payment_info(self, pg_payment_id, expand_on_field=CARD):
        fetch_payment = FetchPaymentRequest(
            payment_id=pg_payment_id,
            expand_on_field=expand_on_field
        )
        response = self._executer.execute(
            fetch_payment, action='fetch')
        return response

    def net_payment_amount(self, pg_payment_id):
        payment = self.fetch_payment_info(pg_payment_id, expand_on_field=None)
        if payment.get("status") != RazorPayPaymentStatus.PAYMENT_CAPTURED:
            return 0
        return convert_paise_to_rupee(payment.get("amount") - payment.get("amount_refunded", 0))

    def issue_refund_by_payout_link(self, payout_link, account_number):
        payout_link = CreatePayoutLinkRequest(
            account_number=account_number,
            contact=payout_link.contact_info,
            amount=payout_link.amount,
            currency=payout_link.currency,
            description="Payout link issued against {}".format(payout_link.service_id),
        )
        response = self._executer.execute(payout_link, action="create")
        return PayoutLinkResponseDTO(
            data=dict(
                payout_id=response.get("id"),
                contact_id=response.get("contact_id"),
                amount=response.get("amount"),
                status=response.get("status"),
                short_url=response.get("short_url"),
                expire_by=response.get("expire_by"),
            )
        )

    def cancel_payout_link(self, pg_payout_id):
        cancel_request = PayoutLinkCancellationRequest(payout_link_id=pg_payout_id)
        response = self._executer.execute(cancel_request, action="cancel")
        return response.get("status") == PayoutLink.CANCELLED

    def get_payout_link(self, pg_payout_id):
        cancel_request = PayoutLinkFetchRequest(payout_link_id=pg_payout_id)
        response = self._executer.execute(cancel_request, action="fetch")
        return PayoutLinkResponseDTO(
            data=dict(
                payout_id=response.get("id"),
                contact_id=response.get("contact_id"),
                amount=response.get("amount"),
                status=response.get("status"),
                short_url=response.get("short_url"),
                expire_by=response.get("expire_by"),
            )
        )

