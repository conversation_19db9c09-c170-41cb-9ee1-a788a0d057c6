#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    RazorpayRequestExecuter
"""
from types import ModuleType

import razorpay
from django.conf import settings
import logging
import time
import random
from functools import wraps

from razorpay.client import capitalize_camel_case

from backends.razorpay import resources

logger = logging.getLogger(__name__)


def razorpay_api_retry(max_retries=3, base_delay=1.0, max_delay=10.0, backoff_factor=2.0):
    """
    Decorator to retry Razorpay API calls on specific transient errors.

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Exponential backoff multiplier
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e

                    # Check if this is a retryable error
                    if not _is_retryable_razorpay_error(e):
                        logger.warning(f"Non-retryable error in {func.__name__}: {str(e)}")
                        raise e

                    if attempt == max_retries:
                        logger.error(f"Max retries ({max_retries}) exceeded for {func.__name__}: {str(e)}")
                        raise e

                    # Calculate delay with exponential backoff and jitter
                    delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                    jitter = random.uniform(0.1, 0.3) * delay
                    total_delay = delay + jitter

                    logger.warning(f"Retryable error in {func.__name__} (attempt {attempt + 1}/{max_retries + 1}): {str(e)}. "
                                 f"Retrying in {total_delay:.2f} seconds...")

                    time.sleep(total_delay)

            # This should never be reached, but just in case
            raise last_exception

        return wrapper
    return decorator


def _is_retryable_razorpay_error(error):
    """
    Determine if a Razorpay error is retryable based on the error message or type.

    Args:
        error: The exception that occurred

    Returns:
        bool: True if the error should be retried, False otherwise
    """
    error_str = str(error).lower()

    # Check for specific Razorpay transient errors
    retryable_patterns = [
        "another payment operation is in progress",
        "bad_request_error",
        "timeout",
        "connection error",
        "temporary failure",
        "service unavailable",
        "rate limit",
        "too many requests",
        "server error",
        "internal server error"
    ]

    for pattern in retryable_patterns:
        if pattern in error_str:
            return True

    # Check for Razorpay specific error types
    if hasattr(error, '__class__'):
        error_class_name = error.__class__.__name__.lower()
        if any(pattern in error_class_name for pattern in ['timeout', 'connection', 'server']):
            return True

    return False


def attach_custom_resources_to_razorpay_client(client):
    for name, module in resources.__dict__.items():
        if isinstance(module, ModuleType) and \
                capitalize_camel_case(name) in module.__dict__:
            klass = module.__dict__[capitalize_camel_case(name)]
            setattr(client, name, klass(client))


class RazorpayRequestExecuter(object):
    """
        RazorpayRequestExecuter
    """

    def __init__(self, auth_token):
        self.access_key = auth_token["access_key"]
        self.secret_key = auth_token["secret_key"]

    @razorpay_api_retry(max_retries=3, base_delay=2.0, max_delay=15.0)
    def execute(self, razorpay_request, action):
        """
        :param razorpay_request:
        :param action:
        :return:
        """
        logger.info('Razorpay action %s for request %s ', action, razorpay_request)
        client = razorpay.Client(auth=(self.access_key, self.secret_key))
        attach_custom_resources_to_razorpay_client(client)
        api = getattr(client, razorpay_request.api_name)
        action_method = getattr(api, action)
        response = action_method(**razorpay_request.data())
        return response
