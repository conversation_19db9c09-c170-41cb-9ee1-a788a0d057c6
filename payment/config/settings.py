# -*- coding:utf-8 -*-
"""
    Prod Config
"""
from .common import *

from . import log_conf
from .logging_conf import LogConfig

ENVIRONMENT = os.environ['ENVIRONMENT']

if ENVIRONMENT == 'local':
    os.environ.setdefault("DEFAULT_DATABASE_ENGINE", "django.db.backends.postgresql_psycopg2")
    # os.environ.setdefault("DEFAULT_DATABASE_USER", "priyanshu")
    # os.environ.setdefault("DEFAULT_DATABASE_PASSWORD", "Pransh##1998")
    os.environ.setdefault("DEFAULT_DATABASE_NAME", "payment")
    # os.environ.setdefault("DEFAULT_DATABASE_HOST", "localhost")
    # os.environ.setdefault("DEFAULT_DATABASE_PORT", "5432")
# <<<<<<< Updated upstream
    os.environ.setdefault("DEFAULT_DATABASE_USER", "crs_user")
    os.environ.setdefault("DEFAULT_DATABASE_PASSWORD", "qtR8T456*wiopj")
    os.environ.setdefault("DEFAULT_DATABASE_HOST", "sharedapps2-rds.treebo.be")
    os.environ.setdefault("DEFAULT_DATABASE_PORT", "5432")
# =======
#     os.environ.setdefault("DEFAULT_DATABASE_USER", "growth")
#     os.environ.setdefault("DEFAULT_DATABASE_PASSWORD", "XYgs1CnY3rw0")
#     os.environ.setdefault("DEFAULT_DATABASE_HOST", "pgb-web-mum.treebohotels.com")
#     os.environ.setdefault("DEFAULT_DATABASE_PORT", "6432")

    os.environ.setdefault("DEBUG", "True")
    os.environ.setdefault("TREEBO_HOME_PAGE", 'https://web-staging.treebo.be/')
    os.environ.setdefault("WEB_BACKEND_ENDPPOINT", 'http://localhost:8083/')
    os.environ.setdefault("RAZORPAY_ACCESS_KEY", "***********************")
    os.environ.setdefault("RAZORPAY_SECRET_PAY", "p6CotYbheCsbIVuOL7yL8BI2")

    #Partpay razorpay account details
    os.environ.setdefault("RAZORPAY_PARTPAY_ACCESS_KEY","***********************")
    os.environ.setdefault("RAZORPAY_PARTPAY_SECRET","CTmXi3L0jZAGkL5Ej7HZlymY")

    os.environ.setdefault("LOG_PATH", BASE_DIR)
    os.environ.setdefault("DEBUG", "True")
    os.environ.setdefault("PHONEPE_ACCEPT_URL", "https://mercury-uat.phonepe.com/v3/debit")
    os.environ.setdefault("PHONEPE_KEY", "8289e078-be0b-484d-ae60-052f117f8deb")
    os.environ.setdefault("PHONEPE_INDEX", "1")
    os.environ.setdefault("PHONEPE_MERCHANTID", "M2306160483220675579140")
    os.environ.setdefault("PHONEPE_HOST", 'https://mercury-uat.phonepe.com')
    os.environ.setdefault("PHONEPE_UI_REDIRECT_URL", "http://localhost:8084/paynow/v2/ui-redirect")
    os.environ.setdefault("PHONEPE_UI_REDIRECT_MODE", "POST")
    os.environ.setdefault("PHONEPE_SERVER_CALLBACK_URL",
                          "http://localhost:8084/paynow/v2/phonepe/confirm")
    os.environ.setdefault("PHONEPE_SERVER_CALL_MODE", "POST")
    os.environ.setdefault("PHONEPE_REFUND_URL", 'https://mercury-uat.phonepe.com/v3/credit/backToSource')

    os.environ.setdefault("AMAZONPAY_MERCHANT_ID", 'A2V1HAX53HC5L6')
    os.environ.setdefault("AMAZONPAY_ACCESS_KEY", '********************')
    os.environ.setdefault("AMAZONPAY_CURRENCY_CODE", 'INR')
    os.environ.setdefault("AMAZONPAY_REGION", 'in')
    os.environ.setdefault("AMAZONPAY_SECRET_KEY", 'Nf+pY9rmrEsAYfAzOBloux29Fc8gRy4GMvGAsZ03')
    os.environ.setdefault("AMAZONPAY_INITIATE_URL", 'https://amazonpay.amazon.in/initiatePayment?')
    os.environ.setdefault("AMAZONPAY_ENCRYPTION_METHOD", 'HmacSHA256')
    os.environ.setdefault("AMAZONPAY_ENCRYPTION_METHOD_VERSION", '2')
    os.environ.setdefault("AMAZONPAY_REFUND_URL", '/OffAmazonPayments_Sandbox/2013-01-01')
    os.environ.setdefault("AMAZONPAY_INIT_HOST_FOR_STRING_TO_SIGN", 'amazonpay.amazon.in')
    os.environ.setdefault("AMAZONPAY_REFUND_HOST_FOR_STRING_TO_SIGN", 'mws.amazonservices.in')
    os.environ.setdefault("AMAZONPAY_REFUND_PROTOCOL", 'https')
    os.environ.setdefault("AMAZON_SANDBOX", 'true')
    os.environ.setdefault("TIMEOUT_AT_AMAZON_UI", '100000')
    os.environ.setdefault("AMAZONPAY_CONFIRMATION_REDIRECT_UI", 'http://localhost:8084/paynow/v2/ui-redirect')
    os.environ.setdefault("PHONEPE_STATUS_URL", 'https://mercury-uat.phonepe.com/v3/transaction/{}/{}/status')
    os.environ.setdefault("WEB_UI_REDIRECT", 'http://localhost:8083/api/v5/checkout/phonepe/ui-redirect/')
    os.environ.setdefault("WEB_CONFIRM_PAYMENT", 'http://localhost:8083/api/v5/checkout/confirmbooking/')
    # os.environ.setdefault("PAYMENT_CONFIRMATION_REDIRECT_UI", 'http://treebo.com/web/v1/confirm')
    os.environ.setdefault("OBJC_DISABLE_INITIALIZE_FORK_SAFETY", 'YES')
    os.environ.setdefault("PAYMENT_SERVICE_HOST", "http://localhost:8084")
    os.environ.setdefault("GROWTH_CALLBACK_URL", "http://staging.treebohotels.com/growth/payment/callback/")
    # phonepe app related configs
    os.environ.setdefault("PHONEPE_APP_MERCHANT_NAME", 'TREEBO')
    os.environ.setdefault("PHONEPE_APP_MERCHANT_ID", 'TREEBOTEST')
    os.environ.setdefault("PHONEPE_APP_KEY", '177f7342-8acd-4e77-9535-691ceecc1093')
    os.environ.setdefault("PHONEPE_APP_INDEX", '1')
    os.environ.setdefault("FETCH_BOOKING_URL", 'api/v1/booking/order/{}/')
    os.environ.setdefault("BOOKING_SUPPORT_URL", 'contactus/?utm_source={utm_source}')
    os.environ.setdefault("BOOKING_TRACKING_URL", 'account/booking-history/{order_id}/?utm_source={utm_source}')
    os.environ.setdefault("PHONEPE_APP_CALLBACK_URL", 'http://localhost:8084/paynow/v2/phonepe-app/confirm')
    os.environ.setdefault("PHONEPE_INITIATE_URL", "https://mercury-uat.phonepe.com/v3/service/initiate")

    os.environ.setdefault("AMAZONPAY_AFFILIATED_MERCHANT_ID", 'A2V1HAX53HC5L6')
    os.environ.setdefault("AMAZONPAY_AFFILIATED_ACCESS_KEY", '********************')
    os.environ.setdefault("*******************************", 'Nf+pY9rmrEsAYfAzOBloux29Fc8gRy4GMvGAsZ03')
    os.environ.setdefault("REWARDS_SERVICE_ENDPOINT", 'https://rewards-staging.treebo.be')

DATABASES = {
    'default': {
        'ENGINE': os.environ['DEFAULT_DATABASE_ENGINE'],
        'NAME': os.environ['DEFAULT_DATABASE_NAME'],
        'USER': os.environ['DEFAULT_DATABASE_USER'],
        'PASSWORD': os.environ['DEFAULT_DATABASE_PASSWORD'],
        'HOST': os.environ['DEFAULT_DATABASE_HOST'],
        'PORT': os.environ['DEFAULT_DATABASE_PORT'],
    }
}
#partpay new account for razorpay
RAZORPAY_PARTPAY_GET_INVOICE_API="https://api.razorpay.com/v1/invoices/"
RAZORPAY_PARTPAY_ACCESS_KEY=os.environ.get('RAZORPAY_PARTPAY_ACCESS_KEY', '***********************')
RAZORPAY_PARTPAY_SECRET=os.environ.get('RAZORPAY_PARTPAY_SECRET', 'CTmXi3L0jZAGkL5Ej7HZlymY')


RAZORPAY_ACCESS_KEY = os.environ.get('RAZORPAY_ACCESS_KEY', '***********************')
RAZORPAY_SECRET_PAY = os.environ.get('RAZORPAY_SECRET_PAY', 'p6CotYbheCsbIVuOL7yL8BI2')


PAYMENT_GATEWAYS = dict()

PAYMENT_GATEWAYS['treebo'] = {
        "razorpay": {
            "default": {
                "access_key": RAZORPAY_ACCESS_KEY,
                "secret_key": RAZORPAY_SECRET_PAY,
            }
        }
    }



LOGGING = LogConfig(os.getenv('LOG_PATH', LOG_ROOT)).get()
PHONEPE_ACCEPT_URL = os.environ['PHONEPE_ACCEPT_URL']
PHONEPE_KEY = os.environ['PHONEPE_KEY']
PHONEPE_MERCHANTID = os.environ['PHONEPE_MERCHANTID']
PHONEPE_UI_REDIRECT_URL = os.environ['PHONEPE_UI_REDIRECT_URL']
PHONEPE_INDEX = os.environ['PHONEPE_INDEX']
PHONEPE_UI_REDIRECT_MODE = os.environ['PHONEPE_UI_REDIRECT_MODE']
PHONEPE_SERVER_CALLBACK_URL = os.environ['PHONEPE_SERVER_CALLBACK_URL']
PHONEPE_SERVER_CALL_MODE = os.environ['PHONEPE_SERVER_CALL_MODE']
PHONEPE_REFUND_URL = os.environ['PHONEPE_REFUND_URL']
PHONEPE_STATUS_URL=os.environ['PHONEPE_STATUS_URL']
PHONEPE_HOST = os.environ['PHONEPE_HOST']
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

#Amazon Pay
AMAZONPAY_MERCHANT_ID = os.environ['AMAZONPAY_MERCHANT_ID']
AMAZONPAY_ACCESS_KEY = os.environ['AMAZONPAY_ACCESS_KEY']
AMAZONPAY_CURRENCY_CODE = os.environ['AMAZONPAY_CURRENCY_CODE']
AMAZONPAY_REGION = os.environ['AMAZONPAY_REGION']
AMAZONPAY_SECRET_KEY = os.environ['AMAZONPAY_SECRET_KEY']
AMAZON_SANDBOX = os.environ['AMAZON_SANDBOX']
AMAZONPAY_INITIATE_URL = os.environ['AMAZONPAY_INITIATE_URL']
AMAZONPAY_REFUND_URL = os.environ['AMAZONPAY_REFUND_URL']
TIMEOUT_AT_AMAZON_UI = os.environ['TIMEOUT_AT_AMAZON_UI']
AMAZONPAY_ENCRYPTION_METHOD = os.environ['AMAZONPAY_ENCRYPTION_METHOD']
AMAZONPAY_ENCRYPTION_METHOD_VERSION = os.environ['AMAZONPAY_ENCRYPTION_METHOD_VERSION']
AMAZONPAY_INIT_HOST_FOR_STRING_TO_SIGN = os.environ['AMAZONPAY_INIT_HOST_FOR_STRING_TO_SIGN']
AMAZONPAY_REFUND_HOST_FOR_STRING_TO_SIGN = os.environ['AMAZONPAY_REFUND_HOST_FOR_STRING_TO_SIGN']
AMAZONPAY_REFUND_PROTOCOL = os.environ['AMAZONPAY_REFUND_PROTOCOL']

#PAYMENT CONFIRMATION UI
AMAZONPAY_CONFIRMATION_REDIRECT_UI = os.environ['AMAZONPAY_CONFIRMATION_REDIRECT_UI']

#WEB ENDPOINT TO TRIGGER PAYMENT CONFIRMATION UPDATES ON PAYMENT GATEWAY RESPONSE
WEB_CONFIRM_PAYMENT = os.environ['WEB_CONFIRM_PAYMENT']

#treebo website redirection on exception
TREEBO_HOME_PAGE = os.environ['TREEBO_HOME_PAGE']
# OBJC_DISABLE_INITIALIZE_FORK_SAFETY = os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY']
GROWTH_CALLBACK_URL = os.environ['GROWTH_CALLBACK_URL']

#Phonepe App related settings
PHONEPE_APP_MERCHANT_NAME = os.environ['PHONEPE_APP_MERCHANT_NAME']
PHONEPE_APP_MERCHANT_ID = os.environ['PHONEPE_APP_MERCHANT_ID']
PHONEPE_APP_KEY = os.environ['PHONEPE_APP_KEY']
PHONEPE_APP_INDEX = os.environ['PHONEPE_APP_INDEX']
FETCH_BOOKING_URL = os.environ['FETCH_BOOKING_URL']
BOOKING_SUPPORT_URL = os.environ['BOOKING_SUPPORT_URL']
BOOKING_TRACKING_URL = os.environ['BOOKING_TRACKING_URL']
PHONEPE_APP_CALLBACK_URL = os.environ['PHONEPE_APP_CALLBACK_URL']
PHONEPE_INITIATE_URL = os.environ['PHONEPE_INITIATE_URL']
WEB_BACKEND_ENDPPOINT = os.environ['WEB_BACKEND_ENDPPOINT']

#merchant id to be used when utm source is amazon pay affiliated i.e treebo_ap
AMAZONPAY_AFFILIATED_MERCHANT_ID = os.environ['AMAZONPAY_AFFILIATED_MERCHANT_ID']
AMAZONPAY_AFFILIATED_ACCESS_KEY = os.environ['AMAZONPAY_AFFILIATED_ACCESS_KEY']
******************************* = os.environ['*******************************']