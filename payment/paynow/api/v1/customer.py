# -*- coding: utf-8 -*-
"""
    CustomerAPI
"""
import logging

from rest_framework import status
from rest_framework.response import Response
from rest_framework.parsers import ParseError

from backends.exceptions import UserAlreadyExistsError
from payment_gateway.constants import PaymentGatewayConfig
from payment_gateway.services.payment_gateway_service import PaymentGatewayService
from paynow.api.api import PaynowAPI
from paynow.api.api_error_responses import APIErrorResponse
from paynow.dto import CustomerRequestDTO, GetCustomerDetailsDTO
from paynow.services import CustomerService
from paynow.services.exceptions import UserNotFoundException


class CustomerAPI(PaynowAPI):
    """
        CustomerAPI
    """

    def _parse_request_data(self, request):
        """
        Safely parse request data and handle JSON parsing errors
        Args:
            request: Django request object
        Returns:
            tuple: (success: bool, data: dict or error_response: Response)
        """
        logger = logging.getLogger(self.__class__.__name__)

        try:
            data = request.data
            return True, data

        # except ParseError as e:
        #     # logger.error("JSON parse error in CustomerAPI: {}".format(str(e)))
        #     error_response = Response({
        #         'errors': [{
        #             'code': 4000,
        #             'message': 'Invalid JSON format',
        #             'developer_message': 'Request body contains malformed JSON: {}'.format(str(e))
        #         }]
        #     }, status.HTTP_400_BAD_REQUEST)
        #     return False, error_response

        # except UnicodeDecodeError as e:
        #     # logger.error("Unicode decode error in CustomerAPI: {}".format(str(e)))
        #     error_response = Response({
        #         'errors': [{
        #             'code': 4000,
        #             'message': 'Invalid request encoding',
        #             'developer_message': 'Request body contains invalid characters: {}'.format(str(e))
        #         }]
        #     }, status.HTTP_400_BAD_REQUEST)
        #     return False, error_response

        except Exception as e:
            # logger.error("Unexpected error parsing request data: {}".format(str(e)))
            error_response = Response({
                'errors': [{
                    'code': 4000,
                    'message': 'Request parsing error',
                    'developer_message': 'Failed to parse request: {}'.format(str(e))
                }]
            }, status.HTTP_400_BAD_REQUEST)
            return False, error_response

    def get(self, request):
        """
        Get the customer details for the given payment gateway
        Args:
            request:

        Returns:

        """
        logger = logging.getLogger(self.__class__.__name__)
        get_customer_dto = GetCustomerDetailsDTO(data=request.query_params)
        if not get_customer_dto.is_valid():
            return Response(get_customer_dto.errors, status.HTTP_400_BAD_REQUEST)

        try:
            gateway_type = get_customer_dto.data.get('gateway') or 'razorpay'
            is_hotel_level = eval(PaymentGatewayService().is_payment_gateway_hotel_level(
                PaymentGatewayConfig.PAYMENT_CONFIG_HOTEL_LEVEL.value))

            if is_hotel_level:
                hotel_id = get_customer_dto.data.get('hotel_id')

            customer_dto = CustomerService(gateway_type=gateway_type, hotel_id=hotel_id if is_hotel_level else None). \
                get_customer(get_customer_dto)
            if not customer_dto.is_valid():
                return Response(customer_dto.errors, status.HTTP_500_INTERNAL_SERVER_ERROR)

            response = Response(data=customer_dto.data,
                                status=status.HTTP_200_OK)

        except UserNotFoundException:
            logger.error("User Not found for given "
                         "credentials {email}: {phone}".format(email=request.query_params.get('email'),
                                                               phone=request.query_params.get('phone')))
            return APIErrorResponse.user_does_not_exist(
                email=request.query_params.get('email'),
                phone=request.query_params.get('phone'))

        except Exception as e:
            logger.error("error occurred while getting"
                         "customer details {email} : {phone}".format(email=request.query_params.get('email'),
                                                                     phone=request.query_params.get('phone')))
            raise e

        return response

    def post(self, request):
        """
        1. Creates a new customer in the given payment gateway
        2. Stores the customer data in the payment database
        Args:
            request:

        Returns:

        """
        logger = logging.getLogger(self.__class__.__name__)
        try:
            request_data = request.data

        # except ParseError as e:
        #     return Response({
        #         'errors': [{
        #             'code': 4000,
        #             'message': 'Invalid JSON format',
        #             'developer_message': 'Request body contains malformed JSON: {}'.format(str(e))
        #         }]
        #     }, status.HTTP_400_BAD_REQUEST)

        # except UnicodeDecodeError as e:
        #     return Response({
        #         'errors': [{
        #             'code': 4000,
        #             'message': 'Invalid request encoding',
        #             'developer_message': 'Request body contains invalid characters: {}'.format(str(e))
        #         }]
        #     }, status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            # logger.error("Unexpected error parsing request data: {}".format(str(e)))
            return Response({
                'errors': [{
                    'code': 4000,
                    'message': 'Request parsing error',
                    'developer_message': 'Failed to parse request: {}'.format(str(e))
                }]
            }, status.HTTP_400_BAD_REQUEST)

        customer_request_dto = CustomerRequestDTO(data=request_data)
        if not customer_request_dto.is_valid():
            return Response(customer_request_dto.errors, status.HTTP_400_BAD_REQUEST)

        try:
            gateway_type = customer_request_dto.data.get('gateway') or 'razorpay'

            is_hotel_level = eval(PaymentGatewayService().is_payment_gateway_hotel_level(
                PaymentGatewayConfig.PAYMENT_CONFIG_HOTEL_LEVEL.value))

            if is_hotel_level:
                hotel_id = customer_request_dto.data.get('hotel_id')

            customer_dto, created = CustomerService(gateway_type=gateway_type, hotel_id=hotel_id if is_hotel_level
                                                    else None).get_or_create_customer(customer_request_dto)
            if not customer_dto.is_valid():
                return Response(customer_dto.errors, status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error("error occurred while creating new "
                         "customer {email} : {phone}".format(email=request_data.get('email'),
                                                             phone=request_data.get('phone')))
            raise e

        if created:
            response = Response(data=customer_dto.data, status=status.HTTP_201_CREATED)
        else:
            response = Response(data=customer_dto.data, status=status.HTTP_200_OK)
        return response
