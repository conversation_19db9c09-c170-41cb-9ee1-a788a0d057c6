#cffi==1.14.5
#cryptography==3.4.7

#click==6.7
#dj-database-url==0.4.0
#Django==1.11.17
#django-extensions==1.8.1
#django-filter==0.13.0
#django-log-request-id==1.3.2
#djangorestframework==3.6.2
#djangorestframework-camel-case==0.2.0
#gevent
#gunicorn==20.0.4
#jsonfield==1.0.3
#logstash-formatter==0.5.16
#newrelic==4.20.1.121
#phonenumbers==8.10.17
#psycogreen==1.0
#psycopg2==2.8.4
#pycryptodome==3.6.1
#pyOpenSSL==18.0.0
#pytz==2018.5
#razorpay==1.2.0
#requests==2.23.0
#simplejson==3.13.2
#treebo-rewards-p3==1.1.13
#treebo-health-check==2.5.3
#treebo-logging-config==1.0.4
sentry-sdk==1.5.12



aenum==3.1.11
Babel==2.10.1
boto3==1.22.8
botocore==1.25.8
certifi==2021.10.8
cffi==1.14.5
chardet==3.0.4
click==6.7
cryptography==3.4.7
dj-database-url==0.4.0
Django==1.11.17
django-extensions==1.8.1
django-filter==0.13.0
django-log-request-id==1.3.2
djangorestframework==3.6.2
djangorestframework-camel-case==0.2.0
enum34==1.1.10
factory-boy==2.10.0
Faker==14.2.1
gevent==20.9.0
greenlet==1.1.2
gunicorn==20.0.4
idna==2.10
IMAPClient==2.2.0
jmespath==0.10.0
jsonfield==1.0.3
logstash-formatter==0.5.16
mail-parser==3.15.0
marshmallow==2.13.5
mock==2.0.0
newrelic==4.20.1.121
pbr==5.11.0
phonenumbers==8.10.17
pika==1.2.1
psycogreen==1.0
psycopg2-binary==2.8.6
pycparser==2.21
pycryptodome==3.6.1
pyOpenSSL==18.0.0
python-dateutil==2.8.2
pytz==2018.3
PyYAML==6.0
razorpay==1.2.0
requests==2.23.0
s3transfer==0.5.2
simplejson==3.15.0
six==1.16.0
SQLAlchemy==1.3.2
thsc==2.2.1
treebo-commons==2.2.0
treebo-health-check==2.5.3
treebo-logging-config==1.0.4
treebo-rewards-p3==1.1.13
typing-extensions==4.1.1
urllib3==1.25.11
zope.event==4.5.0
zope.interface==5.4.0